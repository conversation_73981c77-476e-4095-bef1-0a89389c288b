#!/bin/bash

# 完全离线的draw.io-export设置脚本
# 此脚本会预下载所有必需的资源，确保完全离线运行

set -e

CACHE_DIR="$HOME/.cache/draw.io-export"
BACKUP_DIR="drawio-offline-resources"

# 需要预下载的资源
declare -A RESOURCES=(
    ["export3.html"]="https://app.diagrams.net/export3.html"
    ["app.min.js"]="https://app.diagrams.net/js/app.min.js"
    ["MathJax.js"]="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/MathJax.js?config=TeX-MML-AM_HTMLorMML"
    ["TeX-MML-AM_HTMLorMML.js"]="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/config/TeX-MML-AM_HTMLorMML.js?V=2.7.5"
    ["accessibility-menu.js"]="https://cdn.mathjax.org/mathjax/contrib/a11y/accessibility-menu.js?V=2.7.5"
)

download_resources() {
    echo "正在下载draw.io离线资源..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$CACHE_DIR"
    
    for file in "${!RESOURCES[@]}"; do
        url="${RESOURCES[$file]}"
        echo "下载: $file"
        
        # 下载到备份目录
        curl -L -o "$BACKUP_DIR/$file" "$url" || {
            echo "警告: 无法下载 $file"
            continue
        }
        
        # 复制到缓存目录
        cp "$BACKUP_DIR/$file" "$CACHE_DIR/$file"
    done
    
    # 创建离线包
    tar -czf drawio-offline-cache.tar.gz -C "$BACKUP_DIR" .
    
    echo "离线资源下载完成！"
    echo "备份文件: drawio-offline-cache.tar.gz"
}

install_offline_cache() {
    echo "正在安装离线缓存..."
    
    if [ ! -f "drawio-offline-cache.tar.gz" ]; then
        echo "错误: 找不到离线缓存文件 drawio-offline-cache.tar.gz"
        exit 1
    fi
    
    mkdir -p "$CACHE_DIR"
    tar -xzf drawio-offline-cache.tar.gz -C "$CACHE_DIR"
    
    echo "离线缓存安装完成！"
    echo "缓存位置: $CACHE_DIR"
}

verify_offline() {
    echo "验证离线环境..."
    
    # 检查缓存文件
    missing_files=()
    for file in "${!RESOURCES[@]}"; do
        if [ ! -f "$CACHE_DIR/$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        echo "✅ 所有必需文件都已缓存，可以离线使用"
        return 0
    else
        echo "❌ 缺少以下文件:"
        printf '%s\n' "${missing_files[@]}"
        return 1
    fi
}

test_conversion() {
    echo "测试离线转换功能..."
    
    # 创建测试XML文件
    cat > test-diagram.xml << 'EOF'
<mxfile host="test" modified="2023-01-01T00:00:00.000Z">
  <diagram id="test" name="Test">
    <mxGraphModel dx="800" dy="600" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="测试节点" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="280" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
EOF
    
    # 测试转换
    if command -v drawio >/dev/null 2>&1; then
        echo "正在测试转换..."
        drawio test-diagram.xml -o test-output.png
        
        if [ -f "test-output.png" ]; then
            echo "✅ 离线转换测试成功！"
            rm -f test-diagram.xml test-output.png
            return 0
        else
            echo "❌ 转换失败"
            return 1
        fi
    else
        echo "❌ drawio命令未找到，请先安装draw.io-export"
        return 1
    fi
}

case "$1" in
    download)
        download_resources
        ;;
    install)
        install_offline_cache
        ;;
    verify)
        verify_offline
        ;;
    test)
        test_conversion
        ;;
    setup)
        echo "完整离线设置流程..."
        download_resources
        install_offline_cache
        verify_offline
        test_conversion
        ;;
    *)
        echo "用法: $0 {download|install|verify|test|setup}"
        echo ""
        echo "  download  - 下载所有必需的离线资源"
        echo "  install   - 安装离线缓存文件"
        echo "  verify    - 验证离线环境是否完整"
        echo "  test      - 测试离线转换功能"
        echo "  setup     - 执行完整的离线设置流程"
        exit 1
        ;;
esac
