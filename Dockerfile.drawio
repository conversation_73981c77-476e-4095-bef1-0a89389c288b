# Dockerfile for offline draw.io-export
FROM node:18-alpine

# 安装必要的依赖
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# 设置Puppeteer使用系统安装的Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# 全局安装draw.io-export
RUN npm install -g draw.io-export

# 创建工作目录
WORKDIR /workspace

# 设置入口点
ENTRYPOINT ["drawio"]
