{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"id": "start-node", "type": "ellipse", "x": 300, "y": 100, "width": 150, "height": 80, "angle": 0, "strokeColor": "#5f3dc4", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 123456, "version": 1, "versionNonce": 987654, "isDeleted": false, "boundElements": [{"type": "arrow", "id": "arrow-1"}], "updated": 1698765432, "link": null, "locked": false, "text": "开始", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18}, {"id": "process-node", "type": "rectangle", "x": 300, "y": 250, "width": 150, "height": 80, "angle": 0, "strokeColor": "#1864ab", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 234567, "version": 1, "versionNonce": 876543, "isDeleted": false, "boundElements": [{"type": "arrow", "id": "arrow-1"}, {"type": "arrow", "id": "arrow-2"}], "updated": 1698765432, "link": null, "locked": false, "text": "处理数据", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18}, {"id": "decision-node", "type": "diamond", "x": 300, "y": 400, "width": 150, "height": 100, "angle": 0, "strokeColor": "#5c940d", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 345678, "version": 1, "versionNonce": 765432, "isDeleted": false, "boundElements": [{"type": "arrow", "id": "arrow-2"}, {"type": "arrow", "id": "arrow-3"}, {"type": "arrow", "id": "arrow-4"}], "updated": 1698765432, "link": null, "locked": false, "text": "判断结果", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18}, {"id": "success-node", "type": "rectangle", "x": 150, "y": 550, "width": 150, "height": 80, "angle": 0, "strokeColor": "#087f5b", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 456789, "version": 1, "versionNonce": 654321, "isDeleted": false, "boundElements": [{"type": "arrow", "id": "arrow-3"}, {"type": "arrow", "id": "arrow-5"}], "updated": 1698765432, "link": null, "locked": false, "text": "成功处理", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18}, {"id": "failure-node", "type": "rectangle", "x": 450, "y": 550, "width": 150, "height": 80, "angle": 0, "strokeColor": "#a61e4d", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 567890, "version": 1, "versionNonce": 543210, "isDeleted": false, "boundElements": [{"type": "arrow", "id": "arrow-4"}, {"type": "arrow", "id": "arrow-6"}], "updated": 1698765432, "link": null, "locked": false, "text": "失败处理", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18}, {"id": "end-node", "type": "ellipse", "x": 300, "y": 700, "width": 150, "height": 80, "angle": 0, "strokeColor": "#c92a2a", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "sharp", "seed": 678901, "version": 1, "versionNonce": 432109, "isDeleted": false, "boundElements": [{"type": "arrow", "id": "arrow-5"}, {"type": "arrow", "id": "arrow-6"}], "updated": 1698765432, "link": null, "locked": false, "text": "结束", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18}, {"id": "arrow-1", "type": "arrow", "x": 375, "y": 180, "width": 0, "height": 70, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 789012, "version": 1, "versionNonce": 321098, "isDeleted": false, "boundElements": null, "updated": 1698765432, "link": null, "locked": false, "points": [[0, 0], [0, 70]], "lastCommittedPoint": null, "startBinding": {"elementId": "start-node", "focus": 0, "gap": 1}, "endBinding": {"elementId": "process-node", "focus": 0, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "arrow-2", "type": "arrow", "x": 375, "y": 330, "width": 0, "height": 70, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 890123, "version": 1, "versionNonce": 210987, "isDeleted": false, "boundElements": null, "updated": 1698765432, "link": null, "locked": false, "points": [[0, 0], [0, 70]], "lastCommittedPoint": null, "startBinding": {"elementId": "process-node", "focus": 0, "gap": 1}, "endBinding": {"elementId": "decision-node", "focus": 0, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "arrow-3", "type": "arrow", "x": 300, "y": 450, "width": -75, "height": 100, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 901234, "version": 1, "versionNonce": 109876, "isDeleted": false, "boundElements": null, "updated": 1698765432, "link": null, "locked": false, "points": [[0, 0], [-75, 100]], "lastCommittedPoint": null, "startBinding": {"elementId": "decision-node", "focus": -0.3, "gap": 1}, "endBinding": {"elementId": "success-node", "focus": 0, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "arrow-4", "type": "arrow", "x": 450, "y": 450, "width": 75, "height": 100, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 123456, "version": 1, "versionNonce": 987654, "isDeleted": false, "boundElements": null, "updated": 1698765432, "link": null, "locked": false, "points": [[0, 0], [75, 100]], "lastCommittedPoint": null, "startBinding": {"elementId": "decision-node", "focus": 0.3, "gap": 1}, "endBinding": {"elementId": "failure-node", "focus": 0, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "arrow-5", "type": "arrow", "x": 225, "y": 630, "width": 125, "height": 70, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 234567, "version": 1, "versionNonce": 876543, "isDeleted": false, "boundElements": null, "updated": 1698765432, "link": null, "locked": false, "points": [[0, 0], [125, 70]], "lastCommittedPoint": null, "startBinding": {"elementId": "success-node", "focus": 0, "gap": 1}, "endBinding": {"elementId": "end-node", "focus": -0.3, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "arrow-6", "type": "arrow", "x": 525, "y": 630, "width": -125, "height": 70, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "strokeSharpness": "round", "seed": 345678, "version": 1, "versionNonce": 765432, "isDeleted": false, "boundElements": null, "updated": 1698765432, "link": null, "locked": false, "points": [[0, 0], [-125, 70]], "lastCommittedPoint": null, "startBinding": {"elementId": "failure-node", "focus": 0, "gap": 1}, "endBinding": {"elementId": "end-node", "focus": 0.3, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow"}], "appState": {"viewBackgroundColor": "#ffffff", "gridSize": null}}