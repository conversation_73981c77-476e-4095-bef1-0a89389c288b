#!/bin/bash

# 离线安装draw.io-export的脚本
# 使用方法：
# 1. 在有网络的机器上运行: ./offline-setup.sh download
# 2. 将生成的offline-packages目录传输到离线机器
# 3. 在离线机器上运行: ./offline-setup.sh install

set -e

PACKAGE_DIR="offline-packages"
PACKAGE_NAME="draw.io-export"

download_packages() {
    echo "正在下载 $PACKAGE_NAME 及其依赖..."
    
    # 创建目录
    mkdir -p $PACKAGE_DIR
    cd $PACKAGE_DIR
    
    # 下载主包
    npm pack $PACKAGE_NAME
    
    # 创建临时目录来分析依赖
    mkdir -p temp
    cd temp
    npm init -y
    npm install $PACKAGE_NAME --package-lock-only
    
    # 下载所有依赖
    npm ci --production
    
    # 打包所有内容
    cd ..
    tar -czf ${PACKAGE_NAME}-offline.tar.gz temp/node_modules *.tgz
    
    # 清理
    rm -rf temp
    
    echo "下载完成！离线包保存在: $PACKAGE_DIR/${PACKAGE_NAME}-offline.tar.gz"
    echo "请将此文件传输到离线环境。"
}

install_offline() {
    echo "正在离线安装 $PACKAGE_NAME..."
    
    if [ ! -f "$PACKAGE_DIR/${PACKAGE_NAME}-offline.tar.gz" ]; then
        echo "错误：找不到离线包文件"
        exit 1
    fi
    
    # 解压
    cd $PACKAGE_DIR
    tar -xzf ${PACKAGE_NAME}-offline.tar.gz
    
    # 安装主包
    sudo npm install -g *.tgz
    
    echo "离线安装完成！"
    echo "现在可以使用 'drawio' 命令了。"
}

case "$1" in
    download)
        download_packages
        ;;
    install)
        install_offline
        ;;
    *)
        echo "使用方法："
        echo "  $0 download  # 在有网络的机器上下载包"
        echo "  $0 install   # 在离线机器上安装包"
        exit 1
        ;;
esac
