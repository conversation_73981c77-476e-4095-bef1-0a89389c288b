import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_LLM_API_KEY,
  baseURL: process.env.NEXT_PUBLIC_LLM_BASE_URL,
});

// 生成唯一ID
function generateId(prefix: string = 'shape'): string {
  return `${prefix}:${Math.random().toString(36).substr(2, 9)}`;
}

// 简化的流程图生成 - 直接返回一个基本的示例快照
function createBasicFlowchart(description: string): any {
  // 返回一个简单的示例快照，用户可以在Tldraw中手动编辑
  return {
    document: {
      'shape:start': {
        id: 'shape:start',
        type: 'geo',
        x: 200,
        y: 200,
        props: {
          w: 160,
          h: 80,
          geo: 'ellipse',
          color: 'green',
          fill: 'solid',
          text: '开始',
        },
      },
      'shape:process': {
        id: 'shape:process',
        type: 'geo',
        x: 200,
        y: 350,
        props: {
          w: 160,
          h: 80,
          geo: 'rectangle',
          color: 'blue',
          fill: 'solid',
          text: description.length > 15 ? description.substring(0, 15) + '...' : description,
        },
      },
      'shape:end': {
        id: 'shape:end',
        type: 'geo',
        x: 200,
        y: 500,
        props: {
          w: 160,
          h: 80,
          geo: 'ellipse',
          color: 'red',
          fill: 'solid',
          text: '结束',
        },
      },
    },
    session: {
      currentPageId: 'page:1',
      camera: { x: 0, y: 0, z: 1 },
    },
  };
}

export async function POST(request: NextRequest) {
  try {
    const { description } = await request.json();

    if (!description) {
      return NextResponse.json(
        { error: '请提供流程图描述' },
        { status: 400 }
      );
    }

    // 使用AI优化描述（可选）
    const prompt = `
请将以下流程图描述转换为清晰的步骤列表，每个步骤用逗号分隔：

描述：${description}

要求：
1. 识别流程的开始、处理步骤、判断点和结束
2. 每个步骤简洁明了，不超过15个字
3. 按照逻辑顺序排列
4. 用逗号分隔各个步骤

示例输出：开始,用户登录,验证身份,判断权限,进入系统,结束
`;

    let processedDescription = description;

    try {
      const completion = await openai.chat.completions.create({
        model: process.env.NEXT_PUBLIC_LLM_DEFAULT_MODEL || "Qwen/Qwen3-14B",
        messages: [
          {
            role: "system",
            content: "你是一个专业的流程图分析师，擅长将复杂的流程描述转换为清晰的步骤列表。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 500,
      });

      const aiResponse = completion.choices[0]?.message?.content;
      if (aiResponse && aiResponse.trim()) {
        processedDescription = aiResponse.trim();
      }
    } catch (aiError) {
      console.warn('AI处理失败，使用原始描述:', aiError);
      // 如果AI处理失败，继续使用原始描述
    }

    // 转换为Tldraw数据格式
    const diagramData = createBasicFlowchart(processedDescription);

    console.log('Generated Tldraw data:', JSON.stringify(diagramData, null, 2));

    return NextResponse.json({
      diagramData: diagramData,
      success: true,
      processedDescription: processedDescription
    });

  } catch (error) {
    console.error('生成Tldraw流程图时出错:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
