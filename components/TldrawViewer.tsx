'use client';

import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Tld<PERSON>, Editor, TLUiOverrides, getSnapshot, loadSnapshot, TLEditorSnapshot } from 'tldraw';
import 'tldraw/tldraw.css';

interface TldrawViewerProps {
  className?: string;
  initialSnapshot?: TLEditorSnapshot;
  readOnly?: boolean;
}

export interface TldrawViewerRef {
  exportToPNG: (options?: { scale?: number; background?: boolean }) => Promise<string>;
  exportToSVG: () => Promise<string>;
  loadSnapshot: (snapshot: TLEditorSnapshot) => void;
  getSnapshot: () => TLEditorSnapshot;
  getEditor: () => Editor | null;
}

const TldrawViewer = forwardRef<TldrawViewerRef, TldrawViewerProps>(
  ({ className = '', initialSnapshot, readOnly = false }, ref) => {
    const editorRef = useRef<Editor | null>(null);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      exportToPNG: async (options = {}) => {
        if (!editorRef.current) {
          throw new Error('编辑器未初始化');
        }

        const editor = editorRef.current;
        const shapeIds = editor.getCurrentPageShapeIds();

        if (shapeIds.size === 0) {
          throw new Error('没有可导出的内容');
        }

        try {
          // 使用正确的Tldraw导出API
          const { blob } = await editor.toImage(Array.from(shapeIds), {
            format: 'png',
            scale: options.scale || 2,
            background: options.background !== false,
          });

          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });
        } catch (error) {
          console.error('PNG导出失败:', error);
          throw new Error('PNG导出失败');
        }
      },

      exportToSVG: async () => {
        if (!editorRef.current) {
          throw new Error('编辑器未初始化');
        }

        const editor = editorRef.current;
        const shapeIds = editor.getCurrentPageShapeIds();

        if (shapeIds.size === 0) {
          throw new Error('没有可导出的内容');
        }

        try {
          // 使用正确的Tldraw导出API
          const { blob } = await editor.toImage(Array.from(shapeIds), {
            format: 'svg',
            scale: 1,
            background: true,
          });

          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });
        } catch (error) {
          console.error('SVG导出失败:', error);
          throw new Error('SVG导出失败');
        }
      },

      loadSnapshot: (snapshot: TLEditorSnapshot) => {
        if (editorRef.current && snapshot) {
          try {
            loadSnapshot(editorRef.current.store, snapshot);

            // 调整视图以适应内容
            setTimeout(() => {
              if (editorRef.current) {
                editorRef.current.zoomToFit();
              }
            }, 100);
          } catch (error) {
            console.error('加载快照失败:', error);
          }
        }
      },

      getSnapshot: () => {
        if (!editorRef.current) {
          throw new Error('编辑器未初始化');
        }
        return getSnapshot(editorRef.current.store);
      },

      getEditor: () => editorRef.current,
    }));

    // 自定义UI覆盖（隐藏不需要的工具）
    const uiOverrides: TLUiOverrides = {
      tools(editor, tools) {
        // 只保留基本绘图工具
        return {
          select: tools.select,
          hand: tools.hand,
          draw: tools.draw,
          eraser: tools.eraser,
          arrow: tools.arrow,
          line: tools.line,
          rectangle: tools.rectangle,
          ellipse: tools.ellipse,
          diamond: tools.diamond,
          triangle: tools.triangle,
          star: tools.star,
          hexagon: tools.hexagon,
          cloud: tools.cloud,
          text: tools.text,
          note: tools.note,
          frame: tools.frame,
        };
      },
      actions(editor, actions) {
        // 保留基本操作
        return {
          'zoom-in': actions['zoom-in'],
          'zoom-out': actions['zoom-out'],
          'zoom-to-fit': actions['zoom-to-fit'],
          'zoom-to-selection': actions['zoom-to-selection'],
          'reset-zoom': actions['reset-zoom'],
          undo: actions.undo,
          redo: actions.redo,
          cut: actions.cut,
          copy: actions.copy,
          paste: actions.paste,
          'select-all': actions['select-all'],
          'delete': actions['delete'],
          'duplicate': actions['duplicate'],
        };
      },
    };

    const handleMount = (editor: Editor) => {
      editorRef.current = editor;

      // 如果有初始快照，加载它
      if (initialSnapshot) {
        loadSnapshot(editor.store, initialSnapshot);
      }
    };

    return (
      <div className={`tldraw-container ${className}`} style={{ height: '500px' }}>
        <Tldraw
          onMount={handleMount}
          overrides={uiOverrides}
          options={{
            maxPages: 1, // 限制为单页
          }}
          components={{
            // 可以自定义组件
          }}
        />
      </div>
    );
  }
);

TldrawViewer.displayName = 'TldrawViewer';

export default TldrawViewer;
