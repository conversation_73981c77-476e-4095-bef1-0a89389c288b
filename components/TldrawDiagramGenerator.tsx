'use client';

import React, { useState, useRef } from 'react';
import TldrawViewer, { TldrawViewerRef } from './TldrawViewer';
import { downloadDataUri } from '@/lib/drawio';

export default function TldrawDiagramGenerator() {
  const [description, setDescription] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [diagramSnapshot, setDiagramSnapshot] = useState<any>(null);
  const tldrawRef = useRef<TldrawViewerRef>(null);

  const handleGenerate = async () => {
    if (!description.trim()) {
      setError('请输入流程图描述');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/generate-tldraw-diagram', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ description }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '生成失败');
      }

      if (data.success && data.diagramData) {
        setDiagramSnapshot(data.diagramData);
        // 加载快照到Tldraw
        if (tldrawRef.current) {
          tldrawRef.current.loadSnapshot(data.diagramData);
        }
      } else {
        throw new Error('生成的图表数据无效');
      }
    } catch (err) {
      console.error('生成流程图失败:', err);
      setError(err instanceof Error ? err.message : '生成失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadPNG = async (quality: 'standard' | 'high' = 'high') => {
    if (!tldrawRef.current) {
      setError('图表未加载完成，无法导出');
      return;
    }

    setIsExporting(true);
    setError(null);

    try {
      const scale = quality === 'high' ? 3 : 2;
      const dataUri = await tldrawRef.current.exportToPNG({
        scale,
        background: false, // 透明背景
      });

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const qualityPrefix = quality === 'high' ? 'hq_' : '';
      const filename = `${qualityPrefix}diagram_${timestamp}.png`;

      // 下载文件
      downloadDataUri(dataUri, filename);
    } catch (err) {
      console.error('导出PNG失败:', err);
      setError(err instanceof Error ? err.message : '导出PNG失败');
    } finally {
      setIsExporting(false);
    }
  };

  const handleDownloadSVG = async () => {
    if (!tldrawRef.current) {
      setError('图表未加载完成，无法导出');
      return;
    }

    setIsExporting(true);
    setError(null);

    try {
      const dataUri = await tldrawRef.current.exportToSVG();

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const filename = `diagram_${timestamp}.svg`;

      // 下载文件
      downloadDataUri(dataUri, filename);
    } catch (err) {
      console.error('导出SVG失败:', err);
      setError(err instanceof Error ? err.message : '导出SVG失败');
    } finally {
      setIsExporting(false);
    }
  };

  const handleLoadSample = () => {
    // 创建一个简单的示例图表数据
    const sampleData = {
      schemaVersion: 2,
      store: {
        'document:document': {
          gridSize: 10,
          name: '',
          meta: {},
          id: 'document:document',
          typeName: 'document',
        },
        'page:page': {
          meta: {},
          id: 'page:page',
          name: 'Page 1',
          index: 'a1',
          typeName: 'page',
        },
        'shape:sample1': {
          id: 'shape:sample1',
          type: 'geo',
          x: 200,
          y: 200,
          rotation: 0,
          index: 'a1',
          parentId: 'page:page',
          isLocked: false,
          opacity: 1,
          meta: {},
          props: {
            w: 160,
            h: 80,
            geo: 'ellipse',
            color: 'green',
            labelColor: 'black',
            fill: 'solid',
            dash: 'draw',
            size: 'm',
            font: 'draw',
            text: '开始',
            align: 'middle',
            verticalAlign: 'middle',
            growY: 0,
            url: '',
          },
          typeName: 'shape',
        },
        'shape:sample2': {
          id: 'shape:sample2',
          type: 'geo',
          x: 200,
          y: 350,
          rotation: 0,
          index: 'a2',
          parentId: 'page:page',
          isLocked: false,
          opacity: 1,
          meta: {},
          props: {
            w: 160,
            h: 80,
            geo: 'rectangle',
            color: 'blue',
            labelColor: 'black',
            fill: 'solid',
            dash: 'draw',
            size: 'm',
            font: 'draw',
            text: '处理数据',
            align: 'middle',
            verticalAlign: 'middle',
            growY: 0,
            url: '',
          },
          typeName: 'shape',
        },
        'shape:sample3': {
          id: 'shape:sample3',
          type: 'arrow',
          x: 0,
          y: 0,
          rotation: 0,
          index: 'a1.5',
          parentId: 'page:page',
          isLocked: false,
          opacity: 1,
          meta: {},
          props: {
            dash: 'draw',
            size: 'm',
            fill: 'none',
            color: 'black',
            labelColor: 'black',
            bend: 0,
            start: {
              type: 'binding',
              boundShapeId: 'shape:sample1',
              normalizedAnchor: { x: 0.5, y: 1 },
              isExact: false,
            },
            end: {
              type: 'binding',
              boundShapeId: 'shape:sample2',
              normalizedAnchor: { x: 0.5, y: 0 },
              isExact: false,
            },
            arrowheadStart: 'none',
            arrowheadEnd: 'arrow',
            text: '',
            labelPosition: 0.5,
            font: 'draw',
          },
          typeName: 'shape',
        },
      },
    };

    setDiagramSnapshot(sampleData);
    setDescription('示例流程图：开始 -> 处理数据');

    if (tldrawRef.current) {
      tldrawRef.current.loadSnapshot(sampleData);
    }
  };

  const handleClearAll = () => {
    setDescription('');
    setDiagramSnapshot(null);
    setError(null);

    // 清空画布 - 创建一个空的快照
    const emptySnapshot = {
      document: {},
      session: {}
    };

    if (tldrawRef.current) {
      tldrawRef.current.loadSnapshot(emptySnapshot);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* 标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI 流程图生成器 (Tldraw版)
          </h1>
          <p className="text-gray-600">
            使用AI生成专业的流程图，支持高清PNG导出，完全离线运行
          </p>
        </div>

        {/* 输入区域 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                流程图描述
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="请描述您想要生成的流程图，例如：用户登录流程、订单处理流程等..."
                className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="text-red-600 text-sm">{error}</div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex flex-wrap gap-3">
              <button
                onClick={handleGenerate}
                disabled={isLoading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? '生成中...' : '生成流程图'}
              </button>

              <button
                onClick={handleLoadSample}
                className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                加载示例
              </button>

              <button
                onClick={handleClearAll}
                className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                清空所有
              </button>
            </div>
          </div>
        </div>

        {/* 流程图显示区域 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-900">流程图编辑器</h2>
            <div className="flex space-x-2">
              <div className="relative">
                <button
                  onClick={() => handleDownloadPNG('high')}
                  disabled={isExporting}
                  className="px-4 py-2 bg-purple-600 text-white rounded-l-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isExporting ? '导出中...' : '下载 PNG (高清)'}
                </button>
                <button
                  onClick={() => handleDownloadPNG('standard')}
                  disabled={isExporting}
                  className="px-3 py-2 bg-purple-500 text-white rounded-r-md hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed border-l border-purple-400"
                  title="标准质量PNG"
                >
                  标准
                </button>
              </div>
              <button
                onClick={handleDownloadSVG}
                disabled={isExporting}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下载 SVG
              </button>
            </div>
          </div>

          <TldrawViewer
            ref={tldrawRef}
            className="w-full border border-gray-200 rounded-lg"
            initialSnapshot={diagramSnapshot}
          />
        </div>
      </div>
    </div>
  );
}
