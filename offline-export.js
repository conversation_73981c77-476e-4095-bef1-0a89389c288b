const _ = require('lodash');
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const shelljs = require('shelljs');

const readFile = (file) => new Promise((resolve, reject) => {
  fs.readFile(file, 'utf-8', (err, res) => {
    if (err) {
      reject(err);
    } else {
      resolve(res);
    }
  });
});

const cachePath = (() => {
  if (process.env.XDG_CACHE_HOME)
    return path.join(process.env.XDG_CACHE_HOME, 'draw.io-export');
  if (process.env.HOME)
    return path.join(process.env.HOME, '.cache', 'draw.io-export');
  return path.join(__dirname, '.cache');
})();

// 完全离线的资源映射
const cacheDict = {
  'https://app.diagrams.net/export3.html': 'export3.html',
  'https://app.diagrams.net/js/app.min.js': 'app.min.js',
  'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/MathJax.js?config=TeX-MML-AM_HTMLorMML': 'MathJax.js',
  'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/config/TeX-MML-AM_HTMLorMML.js?V=2.7.5': 'TeX-MML-AM_HTMLorMML.js',
  'https://cdn.mathjax.org/mathjax/contrib/a11y/accessibility-menu.js?V=2.7.5': 'accessibility-menu.js',
};

// 检查所有必需的缓存文件是否存在
const checkOfflineResources = () => {
  const missingFiles = [];
  for (const [url, filename] of Object.entries(cacheDict)) {
    const filePath = path.join(cachePath, filename);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(filename);
    }
  }
  return missingFiles;
};

// 创建本地HTML文件以避免网络请求
const createLocalHTML = () => {
  const localHTMLPath = path.join(cachePath, 'local-export.html');
  
  if (fs.existsSync(localHTMLPath)) {
    return localHTMLPath;
  }
  
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Draw.io Offline Export</title>
    <script src="file://${path.join(cachePath, 'app.min.js')}"></script>
</head>
<body>
    <div id="LoadingComplete" style="display:none;"></div>
    <script>
        // 离线渲染函数
        function render(obj) {
            try {
                const doc = mxUtils.parseXml(obj.xml);
                const graph = new mxGraph();
                const codec = new mxCodec(doc);
                codec.decode(doc.documentElement, graph.getModel());
                
                // 计算边界
                const bounds = graph.getGraphBounds();
                document.getElementById('LoadingComplete').setAttribute('bounds', 
                    JSON.stringify({
                        width: Math.max(bounds.width, 100),
                        height: Math.max(bounds.height, 100)
                    })
                );
                document.getElementById('LoadingComplete').style.display = 'block';
            } catch (e) {
                console.error('Render error:', e);
                // 设置默认边界
                document.getElementById('LoadingComplete').setAttribute('bounds', 
                    JSON.stringify({ width: 800, height: 600 })
                );
                document.getElementById('LoadingComplete').style.display = 'block';
            }
        }
        
        // 全局变量
        window.render = render;
        window.doc = null;
    </script>
</body>
</html>`;
  
  fs.writeFileSync(localHTMLPath, htmlContent);
  return localHTMLPath;
};

module.exports = async ({ file, format, path: p }) => {
  // 检查离线资源
  const missingFiles = checkOfflineResources();
  if (missingFiles.length > 0) {
    throw new Error(`离线资源缺失，请先运行离线设置脚本。缺失文件: ${missingFiles.join(', ')}`);
  }
  
  const fullXml = await readFile(file);
  
  const browser = await puppeteer.launch({
    executablePath: process.env.CHROMIUM_PATH,
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--allow-file-access-from-files'
    ]
  });

  try {
    const page = await browser.newPage();
    
    // 完全阻止所有网络请求
    await page.setRequestInterception(true);
    page.on('request', (interceptedRequest) => {
      const url = interceptedRequest.url();
      const filename = cacheDict[url];
      
      if (filename) {
        const filePath = path.join(cachePath, filename);
        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath);
          interceptedRequest.respond({
            status: 200,
            contentType: url.endsWith('.js') ? 'application/javascript' : 'text/html',
            body: content,
          });
        } else {
          interceptedRequest.abort();
        }
      } else if (url.startsWith('file://')) {
        interceptedRequest.continue();
      } else {
        // 阻止所有其他网络请求
        console.warn(`阻止网络请求: ${url}`);
        interceptedRequest.abort();
      }
    });

    // 使用本地HTML文件
    const localHTMLPath = createLocalHTML();
    await page.goto(`file://${localHTMLPath}`, { waitUntil: 'networkidle0' });

    // 解析XML
    await page.evaluate((xml) => {
      window.doc = mxUtils.parseXml(xml);
    }, fullXml);
    
    const pages = await page.evaluate(() => {
      return parseInt(window.doc.documentElement.getAttribute('pages') || '1');
    });

    const gen = async (fmt, outputPath) => {
      await page.evaluate(() => {
        const dup = window.doc.documentElement.cloneNode(false);
        while (true) {
          const n = window.doc.documentElement.firstChild;
          if (!n) break;
          dup.appendChild(n);
          if (n.nodeType === Node.ELEMENT_NODE) break;
        }
        
        window.render({
          xml: dup.outerHTML,
          format: 'png',
          w: 0,
          h: 0,
          border: 0,
          bg: 'none',
          scale: 1,
        });
      });

      await page.waitForSelector('#LoadingComplete', { timeout: 10000 });
      
      const boundsJson = await page.$eval('#LoadingComplete', (div) => 
        div.getAttribute('bounds')
      );
      
      const bounds = JSON.parse(boundsJson || '{"width":800,"height":600}');
      const w = Math.ceil(bounds.width);
      const h = Math.ceil(bounds.height);

      switch (fmt) {
        case 'png':
          await page.setViewport({ width: w, height: h });
          await page.screenshot({
            omitBackground: true,
            type: 'png',
            fullPage: true,
            path: outputPath,
          });
          break;
        case 'pdf':
          await page.setViewport({ width: w, height: h });
          await page.pdf({
            printBackground: false,
            width: `${w}px`,
            height: `${h + 1}px`,
            margin: { top: '0px', bottom: '0px', left: '0px', right: '0px' },
            path: outputPath,
          });
          break;
        default:
          throw new Error(`不支持的格式: ${fmt}`);
      }
    };

    const m = format.match(/^(?<prefix>.*-)?(?<core>png|pdf)$/);
    if (!m) {
      throw new Error(`无效的格式: ${format}`);
    }
    
    const { prefix, core } = m.groups;
    
    switch (prefix) {
      case undefined:
        await gen(core, p);
        break;
      case 'split-':
        for (let i = 0; i < pages; i++) {
          await gen(core, `${p}${i}.${core}`);
        }
        break;
      default:
        throw new Error(`不支持的格式前缀: ${prefix}`);
    }

  } finally {
    await browser.close();
  }
};
